import React, { useState } from 'react';
import { Button, Text, YStack, XStack } from 'tamagui';
import { useAuth } from '@/provider/AuthProvider';
import { testAuthPersistence, clearAllAuthData, simulateLogin } from '@/utils/authTestUtils';
import { getAuthDebugInfo } from '@/api/apiConfig';

export const AuthDebugPanel = () => {
    const { userJwtToken, isInitialSignIn, initialized, checkAuthStatus, onLogout } = useAuth();
    const [debugInfo, setDebugInfo] = useState<any>(null);
    const [testResult, setTestResult] = useState<string>('');

    const runAuthTest = async () => {
        setTestResult('Running test...');
        const result = await testAuthPersistence();
        setTestResult(JSON.stringify(result, null, 2));
    };

    const getDebugInfo = async () => {
        const info = await getAuthDebugInfo();
        setDebugInfo(info);
    };

    const handleClearAuth = async () => {
        await clearAllAuthData();
        setTestResult('Auth data cleared');
        setDebugInfo(null);
    };

    const handleSimulateLogin = async () => {
        await simulateLogin();
        setTestResult('Demo login simulated');
        // Trigger auth check
        if (checkAuthStatus) {
            await checkAuthStatus();
        }
    };

    const handleCheckAuthStatus = async () => {
        if (checkAuthStatus) {
            const isValid = await checkAuthStatus();
            setTestResult(`Auth status check: ${isValid ? 'Valid' : 'Invalid'}`);
        }
    };

    return (
        <YStack padding="$4" gap="$3" backgroundColor="$gray2" borderRadius="$4">
            <Text fontSize="$6" fontWeight="bold">Auth Debug Panel</Text>
            
            <YStack gap="$2">
                <Text>Current State:</Text>
                <Text fontSize="$3">• Initialized: {initialized ? 'Yes' : 'No'}</Text>
                <Text fontSize="$3">• Has Token: {userJwtToken ? 'Yes' : 'No'}</Text>
                <Text fontSize="$3">• Is Initial Sign In: {isInitialSignIn?.toString() || 'null'}</Text>
            </YStack>

            <XStack gap="$2" flexWrap="wrap">
                <Button size="$3" onPress={runAuthTest}>
                    Test Auth
                </Button>
                <Button size="$3" onPress={getDebugInfo}>
                    Get Debug Info
                </Button>
                <Button size="$3" onPress={handleCheckAuthStatus}>
                    Check Status
                </Button>
            </XStack>

            <XStack gap="$2" flexWrap="wrap">
                <Button size="$3" onPress={handleSimulateLogin} theme="blue">
                    Simulate Login
                </Button>
                <Button size="$3" onPress={handleClearAuth} theme="red">
                    Clear Auth
                </Button>
                <Button size="$3" onPress={onLogout} theme="orange">
                    Logout
                </Button>
            </XStack>

            {debugInfo && (
                <YStack gap="$2">
                    <Text fontWeight="bold">Debug Info:</Text>
                    <Text fontSize="$2" fontFamily="$mono">
                        {JSON.stringify(debugInfo, null, 2)}
                    </Text>
                </YStack>
            )}

            {testResult && (
                <YStack gap="$2">
                    <Text fontWeight="bold">Test Result:</Text>
                    <Text fontSize="$2" fontFamily="$mono">
                        {testResult}
                    </Text>
                </YStack>
            )}
        </YStack>
    );
};
