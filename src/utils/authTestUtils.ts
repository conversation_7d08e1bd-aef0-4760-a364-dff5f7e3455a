import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { getAuthDebugInfo, validateAndRefreshToken, isTokenExpired } from '@/api/apiConfig';

// Test utilities for debugging authentication issues
export const testAuthPersistence = async () => {
    console.log('=== Authentication Persistence Test ===');
    
    try {
        // Get current auth state
        const debugInfo = await getAuthDebugInfo();
        console.log('Current auth state:', debugInfo);
        
        // Check stored tokens
        const accessToken = await AsyncStorage.getItem('user-token');
        const refreshToken = await SecureStore.getItemAsync('refresh-token');
        const isInitialSignIn = await AsyncStorage.getItem('is-initial-sign-in');
        
        console.log('Stored tokens:', {
            hasAccessToken: !!accessToken,
            hasRefreshToken: !!refreshToken,
            isInitialSignIn,
            accessTokenLength: accessToken?.length || 0,
            refreshTokenLength: refreshToken?.length || 0
        });
        
        if (accessToken) {
            console.log('Access token expired:', isTokenExpired(accessToken));
            
            // Test token validation
            const validToken = await validateAndRefreshToken();
            console.log('Token validation result:', !!validToken);
        }
        
        return {
            success: true,
            debugInfo,
            hasTokens: !!accessToken && !!refreshToken
        };
    } catch (error) {
        console.error('Auth test failed:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
};

// Clear all auth data for testing
export const clearAllAuthData = async () => {
    console.log('Clearing all authentication data...');
    
    try {
        await Promise.all([
            AsyncStorage.removeItem('user-token'),
            AsyncStorage.removeItem('user-first-name'),
            AsyncStorage.removeItem('user-last-name'),
            AsyncStorage.removeItem('is-initial-sign-in'),
            SecureStore.deleteItemAsync('refresh-token')
        ]);
        
        console.log('All auth data cleared successfully');
        return true;
    } catch (error) {
        console.error('Error clearing auth data:', error);
        return false;
    }
};

// Simulate a login for testing
export const simulateLogin = async () => {
    console.log('Simulating login...');
    
    try {
        // Use demo token for testing
        const demoToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZGFjNTIzNDAtNjM5Yy00YjE4LWI2NzktODk5YTFmZDViYzcxIiwiZXhwaXJlcyI6MTczNDA3NzkzMi4xNTIxNzU0fQ.ppnsqUI8ITTdGg0n4k4IwR1NGcAlYlll2LpuRTVTdek';
        const demoRefreshToken = 'demo-refresh-token';
        
        await Promise.all([
            AsyncStorage.setItem('user-token', demoToken),
            SecureStore.setItemAsync('refresh-token', demoRefreshToken),
            AsyncStorage.setItem('is-initial-sign-in', 'false')
        ]);
        
        console.log('Demo login simulated successfully');
        return true;
    } catch (error) {
        console.error('Error simulating login:', error);
        return false;
    }
};
