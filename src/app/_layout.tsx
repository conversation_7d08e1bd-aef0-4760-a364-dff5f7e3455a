import '../../tamagui-output.css'
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import {DarkTheme, ThemeProvider} from "@react-navigation/native";
import {Slot, SplashScreen, useRouter, useSegments} from "expo-router";
import {TamaguiProvider, XStack, ZStack} from "tamagui";
import {config} from "../../tamagui.config";
import {useFonts} from "expo-font";
import React, {useEffect, useState} from "react";
import {AuthProvider, useAuth} from "@/provider/AuthProvider";
import {PostHogProvider} from "posthog-react-native";
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Linking, Platform, AppState } from 'react-native';
import * as Application from 'expo-application';
import {getLatestAppVersion} from "@/api/controlAPI";
import { ToastProvider, ToastViewport } from '@tamagui/toast'
import {CurrentToast} from "@/components/CurrentToast";
import {FloatingMusicPlayer} from "@/components/MusicPlayer/FloatingMusicPlayer";


const queryClient = new QueryClient();

SplashScreen.preventAutoHideAsync();

export {ErrorBoundary} from "expo-router";

export const unstable_settings = {
    initialRouteName: "login",
};

async function checkAppVersion() {
    const versionStringToArray = (version: string): number[] => {
        return version.split('.').map(Number);
    };

    const isCurrentVersionGreaterOrEqual = (currentVersion: string, latestVersion: string): boolean => {
        const current = versionStringToArray(currentVersion);
        const latest = versionStringToArray(latestVersion);

        for (let i = 0; i < Math.max(current.length, latest.length); i++) {
            const currentSegment = current[i] || 0;
            const latestSegment = latest[i] || 0;
            if (currentSegment > latestSegment) return true;
            if (currentSegment < latestSegment) return false;
        }
        return true;
    };

    const currentVersion = Application.nativeApplicationVersion;
    const latestVersion = await getLatestAppVersion();

    if (!currentVersion || !latestVersion) {
        console.log('Version information is not available.');
        return;
    }

    console.log('Current version:', currentVersion);
    console.log('Latest version:', latestVersion);

    if (!isCurrentVersionGreaterOrEqual(currentVersion, latestVersion)) {
        // TODO: Check ID's for both platforms
        const storeUrl = Platform.OS === 'ios'
            ? 'itms-apps://itunes.apple.com/app/6477729356'
            : 'market://details?id=YOUR_APP_ID';
        await Linking.openURL(storeUrl);
    }
}

function RootLayout() {
    const [interLoaded, interError] = useFonts({
        Inter: require("@tamagui/font-inter/otf/Inter-Medium.otf"),
        InterBold: require("@tamagui/font-inter/otf/Inter-Bold.otf"),
    });

    useEffect(() => {
        if (interLoaded || interError) {
            SplashScreen.hideAsync();
        }
    }, [interLoaded, interError]);

    if (!interLoaded && !interError) {
        return null;
    }

    return <RootLayoutNav/>;
}

const InitialLayout = () => {
    const {initialized, userJwtToken, isInitialSignIn} = useAuth();
    const router = useRouter();
    const segments = useSegments();
    const isAuthGroup = segments[0] === '(app)';

    useEffect(() => {
        if (!initialized) {
            console.log('Auth not initialized yet, waiting...');
            return;
        }

        console.log('Auth state changed:', {
            userJwtToken: !!userJwtToken,
            isInitialSignIn,
            isAuthGroup,
            currentSegments: segments
        });

        // Prevent unnecessary redirects if already on the correct route
        const currentRoute = segments.join('/');

        if (userJwtToken && isInitialSignIn !== null) {
            if (isInitialSignIn && !currentRoute.includes('onboarding')) {
                console.log('User authenticated, redirecting to onboarding');
                router.replace("/(app)/(onboarding)/CompleteUserProfileScreen");
            } else if (!isInitialSignIn && !currentRoute.includes('tabs') && !currentRoute.includes('aux')) {
                console.log('User authenticated, redirecting to home');
                router.replace("/(app)/(tabs)/Home/HomeScreen");
            }
        } else if (!userJwtToken && isAuthGroup) {
            console.log('User not authenticated, redirecting to login');
            router.replace("/login");
        }
    }, [userJwtToken, initialized, isInitialSignIn, isAuthGroup, segments]);

    useEffect(() => {
        checkAppVersion();
    }, []);

    useEffect(() => {
        const subscription = AppState.addEventListener("change", (nextAppState) => {
            if (nextAppState === "active") {
                checkAppVersion();
            }
        });

        return () => {
            subscription.remove();
        };
    }, []);

    // Show loading screen while auth is initializing
    if (!initialized) {
        return (
            <XStack flex={1} justifyContent="center" alignItems="center" backgroundColor="black">
                <Text color="$yellow10" fontSize="$6">Loading...</Text>
            </XStack>
        );
    }

    return <Slot/>;
}

function RootLayoutNav() {
    const [trackingPermission, setTrackingPermission] = useState<boolean>(false);

    useEffect(() => {
        const fetchTrackingPermission = async () => {
            const permissionStatus = await AsyncStorage.getItem("tracking-permission");
            setTrackingPermission(permissionStatus === "granted");
        };
        fetchTrackingPermission();
    }, []);

    const theme = DarkTheme;

    return (
        <GestureHandlerRootView style={{flex: 1}}>
            <AuthProvider>
                <TamaguiProvider config={config} defaultTheme="dark">
                    <ThemeProvider value={theme}>
                        <ToastProvider swipeDirection="horizontal" duration={6000}>
                            <QueryClientProvider client={queryClient}>
                                {/*<FloatingMusicPlayer*/}
                                {/*    albumCover="https://via.placeholder.com/150"*/}
                                {/*    songTitle="My Song"*/}
                                {/*    artistName="The Artist"*/}
                                {/*/>*/}
                                {trackingPermission && (
                                    <PostHogProvider
                                        apiKey={
                                            process.env.NODE_ENV === "production"
                                                ? "phc_mtu7f6baCyy2V8uzseHsiJiDRqs4hjghqARNu1CgeIV"
                                                : ""
                                        }
                                        options={{
                                            host: "https://eu.posthog.com",
                                        }}
                                    >
                                        <InitialLayout/>
                                    </PostHogProvider>
                                )}
                                {!trackingPermission && <InitialLayout/>}
                            </QueryClientProvider>
                            <CurrentToast />
                            <ToastViewport top="$10" left={0} right={0} />
                        </ToastProvider>
                    </ThemeProvider>
                </TamaguiProvider>
            </AuthProvider>
        </GestureHandlerRootView>
    );
}

export default RootLayout;